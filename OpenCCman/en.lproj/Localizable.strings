// MARK: - IAP
"pro_lifetime" = "Lifetime Pro";
"pro_lifetime_des" = "Pay once, use for a lifetime.";

// MARK: - Help
"help_intro" = "Open Chinese Convert (OpenCC, 開放中文轉換) is an opensource project for conversions between Traditional Chinese, Simplified Chinese and Japanese Kanji (Shinjitai). It supports character-level and phrase-level conversion, character variant conversion and regional idioms among Mainland China, Taiwan and Hong Kong. This is not translation tool between Mandarin and Cantonese, etc.";

"help_features_title" = "Features";
"help_features_content" = "Strictly differentiate between 「one simplified to many traditionals」 and 「one simplified to many variants」.\nCompletely compatible with different variants and can realize dynamic substitution.\nStrictly scrutinize one-simplified-to-multiple-traditional entries, and the principle is 「if it can be divided, then it will be divided」.\nSupport Mainland China, Taiwan, Hong Kong, different variants and regional customary word conversion, such as 「裏」「裡」、「鼠標」「滑鼠」.";

"help_global_service_title" = "Global Service (macOS)";
"help_global_service_intro" = "OpenCCman provides multiple ways to convert Chinese text from any application:";

"help_method1_title" = "Method 1: Global Keyboard Shortcut (Recommended)";
"help_method1_content" = "1. Go to Settings > Global Shortcut to configure your shortcut\n2. Select any Chinese text in any application\n3. Press your configured shortcut (default: ⌘⌥R)\n4. The text will be automatically converted and replaced\n5. OpenCCman will open showing both original and converted text";

"help_method2_title" = "Method 2: Right-click Service";
"help_method2_content" = "1. Select text in any app (Safari, TextEdit, etc.)\n2. Right-click and choose \"Convert Chinese Text with OpenCCman\" from the Services menu\n3. The selected text will be automatically converted and replaced";

"help_service_tip" = "You can also assign a keyboard shortcut to the Right-click Service in System Settings > Keyboard > Keyboard Shortcuts > Services for even faster access.";

"help_conclusion" = "The keyboard shortcut method is faster and more convenient for frequent use.";

// MARK: - Services
"Convert Chinese Text with OpenCCman" = "Convert Chinese Text with OpenCCman";

// MARK: - Shortcut Settings
"Global Shortcut" = "Global Shortcut";
"Shortcut Settings" = "Shortcut Settings";
"Enable Global Shortcut" = "Enable Global Shortcut";
"Keyboard Shortcut" = "Keyboard Shortcut";
"How to Use" = "How to Use";
"Tips" = "Tips";
"Test" = "Test";
"Permissions" = "Permissions";
"Accessibility Permission Required" = "Accessibility Permission Required";
"Open System Preferences" = "Open System Preferences";
"Cancel" = "Cancel";
"Grant Permission" = "Grant Permission";
"Try Again" = "Try Again";
"Refresh" = "Refresh";
"No Text Selected" = "No Text Selected";
"Please select some Chinese text first, then try the shortcut again." = "Please select some Chinese text first, then try the shortcut again.";
"OK" = "OK";
"Accessibility permission granted" = "Accessibility permission granted";
"Accessibility permission required" = "Accessibility permission required";

"Global shortcut feature is only available on macOS" = "Global shortcut feature is only available on macOS";
"shortcut_instructions_enabled_title" = "How to use:";
"shortcut_instructions_enabled_step1" = "1. Enable the global shortcut above";
"shortcut_instructions_enabled_step2" = "2. Click in the shortcut recorder to set your preferred key combination";
"shortcut_instructions_enabled_step3" = "3. Select any Chinese text in any application";
"shortcut_instructions_enabled_step4" = "4. Press your configured shortcut to convert the text";
"shortcut_instructions_enabled_step5" = "5. The converted text will replace the selected text";
"shortcut_instructions_enabled_step6" = "6. OpenCCman will open showing the conversion";

"shortcut_instructions_disabled_title" = "To use the global shortcut feature:";
"shortcut_instructions_disabled_step1" = "1. First grant accessibility permission above";
"shortcut_instructions_disabled_step2" = "2. After granting permission, click \"Refresh\" to update status";
"shortcut_instructions_disabled_step3" = "3. Then enable the global shortcut and configure your shortcut";
"shortcut_instructions_disabled_note" = "Accessibility permission allows OpenCCman to read and convert selected text from other applications.";

"shortcut_tips_title" = "Tips:";
"shortcut_tips_tip1" = "• The shortcut recorder allows you to easily set any key combination";
"shortcut_tips_tip2" = "• Common shortcuts like ⌘⌥R, ⌘⌥T, ⌘⌥C work well";
"shortcut_tips_tip3" = "• Avoid conflicts with system shortcuts";
"shortcut_tips_tip4" = "• The shortcut works globally across all applications";
